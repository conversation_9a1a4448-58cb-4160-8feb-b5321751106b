import sensor, image, time, lcd
from machine import UART

# 初始化硬件
lcd.init()
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)  # 等待摄像头稳定
sensor.set_auto_gain(False)      # 关闭自动增益
sensor.set_auto_whitebal(False)  # 关闭自动白平衡

# 初始化串口通信（与STM32通信）
print("=== UART Debug Info ===")
print("Trying UART initialization...")

try:
    uart = UART(UART.UART1, 115200, 8, 0, 1, timeout=1000, read_buf_len=4096)
    print("UART.UART1 initialized successfully")
except Exception as e:
    print("UART.UART1 init failed:", str(e))
    try:
        uart = UART(3, 115200, timeout=1000, read_buf_len=4096)
        print("UART(3) initialized successfully")
    except Exception as e2:
        print("UART(3) init failed:", str(e2))
        try:
            uart = UART(1, 115200, timeout=1000, read_buf_len=4096)
            print("UART(1) initialized successfully")
        except Exception as e3:
            print("UART(1) init failed:", str(e3))
            uart = None

if uart:
    print("UART object created:", uart)
    print("Testing UART functions...")

    # 测试基本功能
    try:
        print("uart.any():", uart.any())
    except Exception as e:
        print("uart.any() failed:", str(e))

    # 测试写入
    try:
        uart.write(b"INIT_TEST\n")
        print("Write test successful")
    except Exception as e:
        print("Write test failed:", str(e))

    # 等待一下再测试读取
    time.sleep(0.1)
    try:
        available = uart.any()
        print("After write, available bytes:", available)
        if available > 0:
            data = uart.read()
            print("Read back data:", data)
    except Exception as e:
        print("Read test failed:", str(e))

    print("=== UART Init Complete ===")
else:
    print("ERROR: All UART initialization attempts failed!")

# 颜色阈值定义（需要根据实际环境调整）
red_threshold = (30, 100, 15, 127, 15, 127)    # 红色阈值
green_threshold = (35, 80, -128, -10, 0, 127)  # 绿色阈值
yellow_threshold = (20, 30, 0, 127, 0, 127)    # 黄色阈值

# 通信协议定义
COLOR_RED = 0x01
COLOR_GREEN = 0x02
COLOR_YELLOW = 0x03
STM32_FINISH = 0x0A  # STM32完成信号

# 检测状态机
# 0: 等待红色, 1: 等待绿色, 2: 等待黄色, 3: 等待STM32反馈
detect_state = 0
color_detected = False
last_detect_time = 0
last_send_time = 0

def detect_color():
    global detect_state, color_detected, last_detect_time, last_send_time

    img = sensor.snapshot()  # 拍摄一帧图像
    max_area = 0
    detected_color = 0
    target_text = ""

    # 根据当前状态执行不同的检测逻辑
    if detect_state in [0, 1, 2]:
        # 检测对应颜色
        if detect_state == 0:  # 检测红色
            blobs = img.find_blobs([red_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_RED
            target_text = "Red"
        elif detect_state == 1:  # 检测绿色
            blobs = img.find_blobs([green_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_GREEN
            target_text = "Green"
        else:  # 检测黄色
            blobs = img.find_blobs([yellow_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_YELLOW
            target_text = "Yellow"

        # 寻找最大色块
        if blobs:
            for blob in blobs:
                if blob.pixels() > max_area:
                    max_area = blob.pixels()
                    detected_color = target_color
                    # 在图像上绘制色块边界框和十字标记
                    img.draw_rectangle(blob.rect())
                    img.draw_cross(blob.cx(), blob.cy())

        # 确认检测结果（连续检测到同一颜色且面积足够大）
        if detected_color == target_color and max_area > 1000:
            if not color_detected:
                # 首次检测到目标颜色
                color_detected = True
                last_detect_time = time.ticks_ms()
            elif time.ticks_diff(time.ticks_ms(), last_detect_time) > 500:
                # 持续检测到目标颜色超过500ms，确认检测成功
                uart.write(bytes([detected_color]))  # 发送颜色ID到STM32
                last_send_time = time.ticks_ms()
                print("Sent:", target_text, "-> Waiting for STM32...")
                detect_state = 3  # 切换到等待反馈状态
                color_detected = False
        else:
            # 未检测到目标颜色
            color_detected = False

    elif detect_state == 3:
        # 等待STM32完成反馈
        target_text = "Wait STM32"

        # 检查是否收到反馈
        print("=== UART Read Debug ===")
        try:
            available_bytes = uart.any()
            print("Available bytes:", available_bytes)

            if available_bytes > 0:
                print("Reading {} bytes...".format(available_bytes))
                data = uart.read(available_bytes)  # 读取所有可用数据
                print("Raw data:", data)
                print("Data type:", type(data))
                print("Data length:", len(data) if data else 0)

                if data:
                    print("Hex representation:", ' '.join(['0x{:02X}'.format(b) for b in data]))
                    print("Decimal values:", [b for b in data])

                    # 检查是否包含STM32_FINISH
                    print("Looking for STM32_FINISH (0x{:02X} = {})".format(STM32_FINISH, STM32_FINISH))

                    if STM32_FINISH in data:
                        print("*** STM32_FINISH FOUND! ***")
                        print("Received STM32 feedback! Proceed to next color.")
                        detect_state = (detect_state + 1) % 3  # 循环检测红、绿、黄
                    else:
                        print("STM32_FINISH not found in received data")
                        print("Expected: 0x{:02X}, Got: {}".format(STM32_FINISH, [hex(b) for b in data]))
                else:
                    print("Data is None or empty")
            else:
                print("No data available")

        except Exception as e:
            print("Serial read error:", str(e))
            print("Error type:", type(e))

        print("=== End UART Read Debug ===")
        print()

        # 超时处理（修复：将last_send_time的更新移到重发后）
        if time.ticks_diff(time.ticks_ms(), last_send_time) > 20000:
            print("Timeout waiting for STM32 feedback. Resending...")
            # 修正：根据当前状态确定要重发的颜色
            color_to_send = COLOR_RED if detect_state == 0 else COLOR_GREEN if detect_state == 1 else COLOR_YELLOW
            uart.write(bytes([color_to_send]))
            last_send_time = time.ticks_ms()  # 仅在重发时更新时间

    # 在LCD上显示当前状态
    state_text = ["Waiting for Red", "Waiting for Green", "Waiting for Yellow", "Waiting for STM32"]
    img.draw_string(10, 10, state_text[detect_state], color=(255, 255, 255), scale=2)
    lcd.display(img)

# 主循环
loop_count = 0
last_uart_check = 0

print("=== Starting Main Loop ===")
print("Send 0x0A via serial assistant to test...")

while(True):
    detect_color()
    loop_count += 1

    # 每5秒检查一次串口状态（不在状态3时）
    current_time = time.ticks_ms()
    if time.ticks_diff(current_time, last_uart_check) > 5000:
        last_uart_check = current_time

        if detect_state != 3:  # 不在等待反馈状态时
            try:
                available = uart.any()
                if available > 0:
                    print("*** UART Data Available Outside State 3! ***")
                    print("Available bytes:", available)
                    print("Current state:", detect_state)
                    data = uart.read()
                    print("Data:", data)
                    print("Hex:", ' '.join(['0x{:02X}'.format(b) for b in data]) if data else "None")
            except Exception as e:
                print("UART check error:", str(e))

    time.sleep(0.01)  # 短暂延时，减少CPU负载
