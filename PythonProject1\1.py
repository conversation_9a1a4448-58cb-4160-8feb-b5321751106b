import sensor, image, time, lcd
from machine import UART

# 初始化硬件
lcd.init()
sensor.reset()
sensor.set_pixformat(sensor.RGB565)
sensor.set_framesize(sensor.QVGA)
sensor.skip_frames(time = 2000)  # 等待摄像头稳定
sensor.set_auto_gain(False)      # 关闭自动增益
sensor.set_auto_whitebal(False)  # 关闭自动白平衡

# 初始化串口通信（与STM32通信）
uart = UART(UART.UART1, 115200, 8, 0, 1, timeout=1000, read_buf_len=4096)

# 颜色阈值定义（需要根据实际环境调整）
red_threshold = (30, 100, 15, 127, 15, 127)    # 红色阈值
green_threshold = (35, 80, -128, -10, 0, 127)  # 绿色阈值
yellow_threshold = (20, 30, 0, 127, 0, 127)    # 黄色阈值

# 通信协议定义
COLOR_RED = 0x01
COLOR_GREEN = 0x02
COLOR_YELLOW = 0x03
STM32_FINISH = 0x0A  # STM32完成信号

# 检测状态机
# 0: 等待红色, 1: 等待绿色, 2: 等待黄色, 3: 等待STM32反馈
detect_state = 0
color_detected = False
last_detect_time = 0
last_send_time = 0

def detect_color():
    global detect_state, color_detected, last_detect_time, last_send_time

    img = sensor.snapshot()  # 拍摄一帧图像
    max_area = 0
    detected_color = 0
    target_text = ""

    # 根据当前状态执行不同的检测逻辑
    if detect_state in [0, 1, 2]:
        # 检测对应颜色
        if detect_state == 0:  # 检测红色
            blobs = img.find_blobs([red_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_RED
            target_text = "Red"
        elif detect_state == 1:  # 检测绿色
            blobs = img.find_blobs([green_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_GREEN
            target_text = "Green"
        else:  # 检测黄色
            blobs = img.find_blobs([yellow_threshold], pixels_threshold=500, area_threshold=500)
            target_color = COLOR_YELLOW
            target_text = "Yellow"

        # 寻找最大色块
        if blobs:
            for blob in blobs:
                if blob.pixels() > max_area:
                    max_area = blob.pixels()
                    detected_color = target_color
                    # 在图像上绘制色块边界框和十字标记
                    img.draw_rectangle(blob.rect())
                    img.draw_cross(blob.cx(), blob.cy())

        # 确认检测结果（连续检测到同一颜色且面积足够大）
        if detected_color == target_color and max_area > 1000:
            if not color_detected:
                # 首次检测到目标颜色
                color_detected = True
                last_detect_time = time.ticks_ms()
            elif time.ticks_diff(time.ticks_ms(), last_detect_time) > 500:
                # 持续检测到目标颜色超过500ms，确认检测成功
                uart.write(bytes([detected_color]))  # 发送颜色ID到STM32
                last_send_time = time.ticks_ms()
                print("Sent:", target_text, "-> Waiting for STM32...")
                detect_state = 3  # 切换到等待反馈状态
                color_detected = False
        else:
            # 未检测到目标颜色
            color_detected = False

    elif detect_state == 3:
        # 等待STM32完成反馈
        target_text = "Wait STM32"

        # 检查是否收到反馈
        if uart.any():
            try:
                data = uart.read(1)  # 只读取1字节
                if data == bytes([STM32_FINISH]):
                    # 收到完成信号，进入下一种颜色检测
                    print("Received STM32 feedback! Proceed to next color.")
                    detect_state = (detect_state + 1) % 3  # 循环检测红、绿、黄
                else:
                    print("Received unexpected data:", data)
            except Exception as e:
                print("Serial read error: "+str(e))

        # 超时处理（修复：将last_send_time的更新移到重发后）
        if time.ticks_diff(time.ticks_ms(), last_send_time) > 20000:
            print("Timeout waiting for STM32 feedback. Resending...")
            # 修正：根据当前状态确定要重发的颜色
            color_to_send = COLOR_RED if detect_state == 0 else COLOR_GREEN if detect_state == 1 else COLOR_YELLOW
            uart.write(bytes([color_to_send]))
            last_send_time = time.ticks_ms()  # 仅在重发时更新时间

    # 在LCD上显示当前状态
    state_text = ["Waiting for Red", "Waiting for Green", "Waiting for Yellow", "Waiting for STM32"]
    img.draw_string(10, 10, state_text[detect_state], color=(255, 255, 255), scale=2)
    lcd.display(img)

# 主循环
while(True):
    detect_color()
    time.sleep(0.01)  # 短暂延时，减少CPU负载
